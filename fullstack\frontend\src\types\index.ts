// Centralized type definitions for Project Parlay Frontend
// This file serves as the single source of truth for shared interfaces

export interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

export interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

export interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

export interface BoostFormData {
  boost_percentage: number;
  required_picks: number;
  same_sport: boolean;
}

export interface ProtectedFormData {
  protection_percentage: number;
  max_loss: number;
  required_picks: number;
}
