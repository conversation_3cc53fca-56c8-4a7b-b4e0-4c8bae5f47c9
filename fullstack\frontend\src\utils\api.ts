export interface EventData {
  event_id: string;
  event_date: string;
  player_name: string;
  event_team: string;
  handicappers: string[];
  stat_threshold?: number;
  pick_type?: string;
  team_a?: string;
  team_b?: string;
  predictions?: number[];
  stat_thresholds?: number[];
}

export interface TodaysEventsResponse {
  success: boolean;
  message: string;
  events: EventData[];
  date: string;
}

export const fetchTodaysEvents = async (
  customDate?: string
): Promise<TodaysEventsResponse> => {
  try {
    const url = customDate
      ? `/api/todays_events?date=${customDate}`
      : "/api/todays_events";

    console.log("🌐 API Request URL:", url);
    console.log(
      "📅 Custom date parameter:",
      customDate || "none (using current date)"
    );

    const response = await fetch(url);
    console.log(
      "📡 HTTP Response status:",
      response.status,
      response.statusText
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("🔄 Raw API Response:", JSON.stringify(data, null, 2));

    return data;
  } catch (error) {
    console.error("💥 Error fetching today's events:", error);
    throw error;
  }
};

export interface FavoritesResponse {
  success: boolean;
  message: string;
  favorites: number[];
}

export const fetchFavorites = async (): Promise<FavoritesResponse> => {
  try {
    const response = await fetch("/api/favorites");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error fetching favorites:", error);
    throw error;
  }
};

export const saveFavorites = async (
  favorites: number[]
): Promise<FavoritesResponse> => {
  try {
    const response = await fetch("/api/favorites", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ favorites }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error saving favorites:", error);
    throw error;
  }
};

export interface HandicapperProfileResponse {
  success: boolean;
  message: string;
  handicapper: {
    id: number;
    name: string;
    accuracy: string;
    sports: string;
    picks: any[];
  };
}

export const fetchHandicapperProfile = async (
  id: number
): Promise<HandicapperProfileResponse> => {
  try {
    const response = await fetch(`/api/handicappers/${id}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error fetching handicapper profile:", error);
    throw error;
  }
};
